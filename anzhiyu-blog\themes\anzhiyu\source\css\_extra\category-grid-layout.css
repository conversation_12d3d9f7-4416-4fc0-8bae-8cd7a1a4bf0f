/* 分类卡片网格布局优化 */

/* 基础网格布局 - 智能自适应 */
.card-categories ul.card-category-list {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin: 0;
  padding: 0;
  list-style: none;
  justify-items: stretch;
  align-items: stretch;
}

/* 特殊布局处理 - 针对不同数量的分类 */
/* 7个分类时的特殊布局 */
.card-categories ul.card-category-list .card-category-list-item:nth-child(7):last-child {
  grid-column: 2 / 3;
}

/* 5个分类时的特殊布局 */
.card-categories ul.card-category-list .card-category-list-item:nth-child(5):last-child {
  grid-column: 2 / 3;
}

/* 确保网格项目均匀分布 */
.card-categories ul.card-category-list {
  grid-auto-rows: 1fr;
}

/* 分类卡片样式优化 */
.card-categories ul.card-category-list > .card-category-list-item {
  width: 100%;
  margin: 0;
}

.card-categories ul.card-category-list > .card-category-list-item a {
  display: flex !important;
  flex-direction: column !important;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  text-align: center;
  min-height: 70px;
  border-radius: 12px;
  border: var(--style-border);
  background: var(--anzhiyu-card-bg);
  color: var(--font-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

/* 分类名称样式 */
.card-categories ul.card-category-list > .card-category-list-item a span:first-child {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  width: 100%;
  text-align: center;
  line-height: 1.2;
}

/* 分类数量样式 */
.card-categories ul.card-category-list > .card-category-list-item a span:last-child {
  font-size: 12px;
  opacity: 0.7;
  font-weight: 500;
  background: var(--anzhiyu-main-op);
  color: var(--anzhiyu-main);
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 20px;
}

/* 悬停效果 */
.card-categories ul.card-category-list > .card-category-list-item a:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--anzhiyu-shadow-main);
  border-color: var(--anzhiyu-main);
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
}

.card-categories ul.card-category-list > .card-category-list-item a:hover span {
  color: var(--anzhiyu-white);
}

.card-categories ul.card-category-list > .card-category-list-item a:hover span:last-child {
  background: rgba(255, 255, 255, 0.2);
  color: var(--anzhiyu-white);
}

/* 光效动画 */
.card-categories ul.card-category-list > .card-category-list-item a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.card-categories ul.card-category-list > .card-category-list-item a:hover::before {
  left: 100%;
}

.card-categories ul.card-category-list > .card-category-list-item a span {
  position: relative;
  z-index: 2;
}

/* 响应式设计 - 优化网格布局 */
@media screen and (min-width: 1200px) {
  .card-categories ul.card-category-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a {
    min-height: 75px;
    padding: 18px 12px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a span:first-child {
    font-size: 15px;
  }
}

@media screen and (max-width: 1199px) and (min-width: 768px) {
  .card-categories ul.card-category-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
}

@media screen and (max-width: 767px) and (min-width: 481px) {
  .card-categories ul.card-category-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a {
    padding: 12px 8px;
    min-height: 60px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a span:first-child {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a span:last-child {
    font-size: 11px;
    padding: 1px 6px;
  }
}

@media screen and (max-width: 480px) {
  .card-categories ul.card-category-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a {
    padding: 10px 6px;
    min-height: 55px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a span:first-child {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .card-categories ul.card-category-list > .card-category-list-item a span:last-child {
    font-size: 10px;
    padding: 1px 5px;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .card-categories ul.card-category-list > .card-category-list-item a:hover span:last-child {
  background: rgba(255, 255, 255, 0.15);
}

/* 动画效果 */
.card-categories ul.card-category-list > .card-category-list-item {
  animation: fadeInUp 0.6s ease forwards;
}

.card-categories ul.card-category-list > .card-category-list-item:nth-child(1) { animation-delay: 0.1s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(2) { animation-delay: 0.2s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(3) { animation-delay: 0.3s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(4) { animation-delay: 0.4s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(5) { animation-delay: 0.5s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(6) { animation-delay: 0.6s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(7) { animation-delay: 0.7s; }
.card-categories ul.card-category-list > .card-category-list-item:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 确保子分类也使用网格布局 */
.card-categories .card-category-list.child {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  padding: 8px 0 0 16px;
}

.card-categories .card-category-list.child > .card-category-list-item a {
  min-height: 50px;
  padding: 10px 8px;
  font-size: 12px;
}
